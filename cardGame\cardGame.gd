extends Node

const Player = preload("res://cardGame/player.gd")
const CardScene = preload("res://cardGame/Card.tscn")
const PlayerUIScene = preload("res://cardGame/PlayerUI.tscn")

var players: Array = []
var prompts: Array = []
var responses: Array = []
var current_judge_index: int = 0  # Kept for compatibility, but no longer used for judging
var current_prompt: Dictionary = {}
var judge_timer: Timer
var played_cards = {}  # Made this a class variable so it can be accessed in the timeout function

# Multiplayer variables
var multiplayer_manager
var is_multiplayer_game = false
var room_code = ""
var waiting_for_players = false
var current_round = 0
var game_status = "Waiting for Players"
var max_score = 5  # Number of rounds to win the game
var timer_end = 0   # When the current timer will end
var last_processed_status = ""  # Track the last processed game status to prevent duplicate processing
var is_processing_state = false  # Flag to prevent concurrent state processing
var last_timer_update = 0  # Track the last timer update to prevent UI flashing
var prompt_sent_for_round = 0  # Track which round we've already sent a prompt for
var should_display_responses = false  # Flag to control when responses should be displayed
var last_displayed_responses_count = 0  # Track how many responses we've displayed to avoid duplicates

# Add a new variable to track used prompts
var used_prompt_indices = []  # Tracks which indices have been used
var prompt_selection_index = 0  # Current index for sequential selection

# UI references
@onready var prompt_label = $CurrentPromptDisplay/Prompt
@onready var judge_label = $Judge
@onready var timer_label = $Timer/Label
@onready var player_container = $HBoxContainer
@onready var cards_container = $HBoxContainer2
@onready var played_cards_container = $TextureRect
@onready var room_code_display = $RoomCodeDisplay
@onready var current_prompt_display = $CurrentPromptDisplay
@onready var winning_response_display = $WinningResponseDisplay
@onready var judge_asks_label = $JudgeAsksLabel

func _ready():
	# Add a key press handler for testing
	set_process_input(true)
	# Create judge timer if it doesn't exist
	if not judge_timer:
		judge_timer = Timer.new()
		judge_timer.name = "JudgeTimer"
		judge_timer.one_shot = true
		judge_timer.wait_time = 30.0
		judge_timer.timeout.connect(_on_judge_timer_timeout)
		add_child(judge_timer)
		print("Judge timer created")

	# Make sure the current_prompt_display is properly initialized
	current_prompt_display = get_node_or_null("CurrentPromptDisplay")
	if current_prompt_display:
		print("CurrentPromptDisplay found in scene")
		# Initially hide it until we have a prompt
		current_prompt_display.visible = false
		print("Set CurrentPromptDisplay.visible = false initially")
	else:
		print("ERROR: CurrentPromptDisplay node not found in scene!")
		# Try to find it by searching all children
		for child in get_children():
			print("Child node: ", child.name)
			if child.name == "CurrentPromptDisplay":
				print("Found CurrentPromptDisplay in children")
				current_prompt_display = child
				current_prompt_display.visible = false
				print("Set CurrentPromptDisplay.visible = false initially")
				break

	# Make sure the judge_asks_label is properly initialized
	judge_asks_label = get_node_or_null("JudgeAsksLabel")
	if judge_asks_label:
		print("JudgeAsksLabel found in scene")
		# Initially hide it until we have a prompt
		judge_asks_label.visible = false
		print("Set JudgeAsksLabel.visible = false initially")
	else:
		print("ERROR: JudgeAsksLabel node not found in scene!")
		# Try to find it by searching all children
		for child in get_children():
			if child.name == "JudgeAsksLabel":
				print("Found JudgeAsksLabel in children")
				judge_asks_label = child
				judge_asks_label.visible = false
				print("Set JudgeAsksLabel.visible = false initially")
				break

	# Create a timer for updating the UI if it doesn't exist
	var existing_ui_timer = get_node_or_null("UITimer")
	if not existing_ui_timer:
		var ui_timer = Timer.new()
		ui_timer.name = "UITimer"
		ui_timer.one_shot = false
		ui_timer.wait_time = 0.5  # Update twice per second
		ui_timer.timeout.connect(_update_timer_display)
		ui_timer.autostart = true
		add_child(ui_timer)
		print("UI timer created with wait time: ", ui_timer.wait_time)

	# Note: Removed ResponseCheckTimer - now using Firebase streaming for real-time updates
	print("Using Firebase streaming instead of polling timer for responses")

	# Check if UI elements exist
	if not prompt_label or not judge_label or not timer_label or not played_cards_container:
		push_error("UI elements not found. Check scene structure.")
		return

	# Hide player container - we don't want to show player columns
	if player_container:
		player_container.visible = false

	# Hide cards container - we don't need this either
	if cards_container:
		cards_container.visible = false

	# Hide room code display by default
	if room_code_display:
		room_code_display.visible = false

	# Hide timer label by default
	if timer_label:
		timer_label.visible = false

	# Load prompts and responses
	prompts = load_prompts()
	responses = load_responses()

	# Debug information
	print("Loaded prompts: ", prompts.size())
	print("Loaded responses: ", responses.size())

	# Shuffle the cards
	randomize()
	prompts.shuffle()
	responses.shuffle()

	# Check if multiplayer manager exists
	multiplayer_manager = get_node_or_null("/root/MultiplayerManager")
	if multiplayer_manager:
		print("Multiplayer manager found, initializing multiplayer game")
		is_multiplayer_game = true
		multiplayer_manager.initialize_game(self)

		# Connect to game state changes
		if multiplayer_manager.firebase:
			print("Attempting to connect to game_status_changed signal")
			var signal_list = multiplayer_manager.firebase.get_signal_list()
			# print("Available signals: ", signal_list) # Optional: for debugging

			var signal_found = false
			for sig in signal_list:
				if sig["name"] == "game_status_changed":
					signal_found = true
					break

			if signal_found:
				print("Signal game_status_changed found, connecting to handle_game_state_change...")
				# Ensure not already connected before connecting
				if not multiplayer_manager.firebase.is_connected("game_status_changed", Callable(self, "handle_game_state_change")):
					var error_code = multiplayer_manager.firebase.connect("game_status_changed", Callable(self, "handle_game_state_change"))
					if error_code == OK:
						print("Signal connected successfully to handle_game_state_change")
					else:
						printerr("Failed to connect game_status_changed to handle_game_state_change, error: ", error_code)
				else:
					print("Signal game_status_changed already connected to handle_game_state_change")
			else:
				push_error("Signal game_status_changed not found in firebase_manager")

			# Connect directly to the firebase manager for status changes - THIS IS REDUNDANT
			# Commenting out the connection to _on_direct_game_status_changed
			# if multiplayer_manager.firebase.has_signal("game_status_changed"): # Check if signal exists
			# 	print("Checking connection for _on_direct_game_status_changed")
			# 	if not multiplayer_manager.firebase.is_connected("game_status_changed", Callable(self, "_on_direct_game_status_changed")):
			# 		# var error_code_direct = multiplayer_manager.firebase.connect("game_status_changed", Callable(self, "_on_direct_game_status_changed"))
			# 		# if error_code_direct == OK:
			# 		# 	print("Signal connected successfully to _on_direct_game_status_changed")
			# 		# else:
			# 		# 	printerr("Failed to connect game_status_changed to _on_direct_game_status_changed, error: ", error_code_direct)
			# 		print("Redundant connection to _on_direct_game_status_changed is now commented out.")
			# 	else:
			# 		print("Signal game_status_changed was already connected to _on_direct_game_status_changed (now commented out).")


			# Connect to the player_response_used signal
			print("Connecting to player_response_used signal")
			multiplayer_manager.firebase.player_response_used.connect(self._on_player_response_used)

			# Connect to Firebase streaming signals
			print("Connecting to Firebase streaming signals...")

			# Connect to submitted responses updates
			if multiplayer_manager.firebase.has_signal("submitted_responses_updated"):
				multiplayer_manager.firebase.submitted_responses_updated.connect(self._on_submitted_responses_updated)
				print("Connected to submitted_responses_updated signal")

			# Connect to judge index updates
			if multiplayer_manager.firebase.has_signal("judge_index_updated"):
				multiplayer_manager.firebase.judge_index_updated.connect(self._on_judge_index_updated)
				print("Connected to judge_index_updated signal")

			# Connect to players updates
			if multiplayer_manager.firebase.has_signal("players_updated"):
				multiplayer_manager.firebase.players_updated.connect(self._on_players_updated)
				print("Connected to players_updated signal")

			# Connect to winner updates
			if multiplayer_manager.firebase.has_signal("winner_updated"):
				multiplayer_manager.firebase.winner_updated.connect(self._on_winner_updated)
				print("Connected to winner_updated signal")

			# Connect to winning response updates
			if multiplayer_manager.firebase.has_signal("winning_response_updated"):
				multiplayer_manager.firebase.winning_response_updated.connect(self._on_winning_response_updated)
				print("Connected to winning_response_updated signal")

			# Connect to new game with different players signal
			if multiplayer_manager.firebase.has_signal("new_game_different_players"):
				multiplayer_manager.firebase.new_game_different_players.connect(self._on_new_game_different_players)
				print("Connected to new_game_different_players signal")

			# Start Firebase streaming for real-time updates
			multiplayer_manager.firebase.start_firebase_streaming()
			print("Started Firebase streaming for real-time updates")

			# Connect to the judge_index_updated signal if it exists
			print("Checking for judge_index_updated signal")
			var judge_signal_found = false
			for sig in multiplayer_manager.firebase.get_signal_list():
				if sig["name"] == "judge_index_updated":
					judge_signal_found = true
					break

			if judge_signal_found:
				print("Signal judge_index_updated found, connecting...")
				multiplayer_manager.firebase.judge_index_updated.connect(self._on_judge_index_updated)
				print("Judge index signal connected successfully")
			else:
				print("judge_index_updated signal not found")

		# Start a new multiplayer game
		start_multiplayer_game()

	# Log for debugging
	print("Player UI setup skipped - player columns are hidden")

	# Make sure player container is hidden
	if player_container:
		player_container.visible = false

func update_player_hand(player):
	# This function now only updates the internal state
	# We no longer display player hands in the UI

	# We still need to make sure the player has cards in their hand
	# but we don't need to create UI elements for them

	# This function is kept for compatibility with existing code
	# that calls it, but it doesn't update the UI anymore

	# Log for debugging
	print("Updated internal hand state for player: " + player.pname + ", hand size: " + str(player.hand.size()))

func setup_player_ui():
	# This function is kept for compatibility with existing code that calls it
	# Player UI setup has been simplified - we no longer show player columns
	print("setup_player_ui called - player UI setup skipped (player columns are hidden)")
	
	# Make sure player container is hidden
	if player_container:
		player_container.visible = false

func _on_card_selected(player, card, _card_ui):
	# Allow selection during response phase
	if not judge_timer.is_stopped():
		# Play the card
		player.remove_card_from_hand(card)
		played_cards[player] = card

		# Update internal state
		update_player_hand(player)

		# Add the card to the played cards UI
		add_played_card_to_ui(player, card)

		# Check if all players have submitted responses
		check_all_responses_submitted()

func add_played_card_to_ui(player, card):
	# Create a container for the card
	var card_container = CardScene.instantiate()
	card_container.name = player.pname + "_card"

	# Set the card text
	if card_container.has_node("VBoxContainer/CardText"):
		card_container.get_node("VBoxContainer/CardText").text = card.get("response", "Error")

	# Make the card look better
	if card_container.has_node("VBoxContainer"):
		var vbox = card_container.get_node("VBoxContainer")
		vbox.add_theme_constant_override("separation", 10)

		# Add player name label
		var name_label = Label.new()
		name_label.text = player.pname
		name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		name_label.add_theme_font_size_override("font_size", 16)
		vbox.add_child(name_label)

	# Hide any select button if it exists
	if card_container.has_node("VBoxContainer/SelectButton"):
		card_container.get_node("VBoxContainer/SelectButton").visible = false

	# Add to the played cards container
	played_cards_container.add_child(card_container)

	# Store a reference to the player on the card container
	card_container.set_meta("player_ref", player)

# Function to handle displaying submitted responses from Firebase
func _on_submitted_responses_updated(submitted_responses_data: Array):
	print("\n===== SUBMITTED RESPONSES UPDATED =====")
	print("Processing submitted responses from Firebase: ", submitted_responses_data)
	print("Current game status: ", game_status)
	print("should_display_responses: ", should_display_responses)
	print("Number of existing children in played_cards_container: ", played_cards_container.get_children().size())

	# Check if we should display responses based on game state
	if not should_display_responses:
		print("Not displaying responses as should_display_responses is false.")
		return

	if submitted_responses_data == null or submitted_responses_data.is_empty():
		print("No submitted responses to display or data is null.")
		return

	# Check if we already have the same number of responses displayed
	var current_responses_count = submitted_responses_data.size()
	if current_responses_count == last_displayed_responses_count:
		print("Same number of responses already displayed (", current_responses_count, "), skipping update.")
		return

	# Clear existing cards in the container to display the fresh set
	# This ensures that if the function is called multiple times, we don't get duplicate cards
	for child in played_cards_container.get_children():
		child.queue_free()

	# Update the tracking variable
	last_displayed_responses_count = current_responses_count

	print("Displaying " + str(submitted_responses_data.size()) + " responses.")
	for response_data in submitted_responses_data:
		# Ensure the response_data is a dictionary and has the expected keys
		if not typeof(response_data) == TYPE_DICTIONARY:
			printerr("Invalid response data format: expected Dictionary, got ", typeof(response_data), " for data: ", response_data)
			continue
		if not response_data.has("player_name") or not response_data.has("text"):
			printerr("Invalid response data format: missing 'player_name' or 'text'. Data: ", response_data)
			continue

		var player_name = response_data.get("player_name", "Unknown Player")
		var response_text = response_data.get("text", "No response text")

		# Create response text label (no player name)
		var response_label = Label.new()
		response_label.text = response_text
		response_label.name = player_name + "_response_label"  # Unique name for tracking
		response_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		response_label.add_theme_font_size_override("font_size", 16)
		response_label.add_theme_color_override("font_color", Color.WHITE)
		response_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
		response_label.custom_minimum_size = Vector2(200, 0)  # Set minimum width for wrapping

		played_cards_container.add_child(response_label)
		print("REAL-TIME: Added response to UI - '", response_text, "' (", current_responses_count, " total responses now displayed)")

# Check if all players (except the judge) have submitted responses
func check_all_responses_submitted():
	print("Checking if all players have submitted responses...")

	# Skip if we're not in the right game state
	if game_status != "Waiting for Player Responses":
		print("Not in Waiting for Player Responses state, skipping check")
		return

	# Count how many players have submitted responses
	var responses_count = played_cards.size()
	var expected_responses = players.size() - 1  # All players except the judge

	print("Responses received: ", responses_count, "/", expected_responses)

	# If all players have submitted responses, update the game status
	if responses_count >= expected_responses:
		print("\n\nAll players have submitted responses, changing status to Ready for Judging\n\n")

		# Update local game status
		game_status = "Ready for Judging"
		last_processed_status = "Ready for Judging"

		# Update Firebase
		if multiplayer_manager:
			print("Updating game status via multiplayer_manager")
			multiplayer_manager.update_game_status("Ready for Judging")

			# Use direct Firebase update as backup
			if multiplayer_manager.firebase:
				print("Using direct Firebase update as backup")
				multiplayer_manager.firebase.update_game_data("status", "Ready for Judging")

		# Stop the timer
		judge_timer.stop()
		timer_label.text = "All responses received, ready for judging"

# Handle the timer timeout - handles different game states
func _on_judge_timer_timeout():
	print("Timer expired in state: " + game_status)

	# Handle different game states
	match game_status:
		"Waiting for Player Responses":
			print("Response time is up! Moving to next round.")
			timer_label.text = "Response time is up! Moving to next round..."

			# In multiplayer games, we need to move to the next round
			if is_multiplayer_game and multiplayer_manager:
				print("\n\nTIMER EXPIRED: Starting new round\n\n")

				# First, update the status to show responses are done
				print("\n\nUPDATING STATUS TO: Ready for Judging\n\n")

				# Update local game status
				game_status = "Ready for Judging"
				last_processed_status = "Ready for Judging"

				# Update Firebase
				multiplayer_manager.update_game_status("Ready for Judging")

				# Use direct Firebase update as backup
				if multiplayer_manager.firebase:
					print("Using direct Firebase update as backup")
					multiplayer_manager.firebase.update_game_data("status", "Ready for Judging")

				# Wait a moment to let the status update
				await get_tree().create_timer(2.0).timeout

				# Then start a new round by setting status to Ready
				print("\n\nUPDATING STATUS TO: Ready\n\n")

				# Update local game status
				game_status = "Ready"
				last_processed_status = "Ready"

				# Update Firebase
				multiplayer_manager.update_game_status("Ready")

				# Use direct Firebase update as backup
				multiplayer_manager.firebase.update_game_data("status", "Ready")
				return

			# For local games, just move to the next round
			_on_next_round()

		"Round Over":
			print("Judging time is up! Moving to next round.")
			timer_label.text = "Judging time is up! Moving to next round..."

			# In multiplayer games, the web client handles the judging
			if is_multiplayer_game:
				return

			# For local games, just move to the next round
			_on_next_round()

		_:
			print("Timer expired in unhandled state: " + game_status)
			timer_label.text = "Timer expired..."

# Direct handler for game status changes from Firebase
func _on_direct_game_status_changed(new_status):
	print("DIRECT HANDLER CALLED: Game status changed to: ", new_status)

	# Check if this is the same status we just processed
	if new_status == last_processed_status:
		print("WARNING: Already processed this status, ignoring direct call: ", new_status)

		# Special case for Ready state - we want to process it again if we're in Waiting for Players
		if new_status == "Ready" and game_status == "Waiting for Players":
			print("Special case: Processing Ready state even though it was last processed (direct call)")
			# Continue processing
		else:
			return

	# Call the main handler
	handle_game_state_change(new_status)

# Handle game state changes from Firebase
func handle_game_state_change(new_status):
	print("HANDLER CALLED: Game status changed to: ", new_status)

	# Check if we're already processing a state change
	if is_processing_state:
		print("WARNING: Already processing a state change, ignoring new state: ", new_status)
		return

	# Check if this is the same status we just processed
	if new_status == last_processed_status:
		print("WARNING: Already processed this status, ignoring: ", new_status)

		# Special case for Ready state - we want to process it again if we're in Waiting for Players
		if new_status == "Ready" and game_status == "Waiting for Players":
			print("Special case: Processing Ready state even though it was last processed")
			# Continue processing
		else:
			return

	# Set the processing flag
	is_processing_state = true

	# First, make sure we have the latest player data and judge index from Firebase
	if multiplayer_manager and multiplayer_manager.firebase:
		print("Checking for players and judge index in Firebase")

		# Use our dedicated function to update the judge label with the latest data from Firebase
		await update_judge_asks_label()

		# Get the players array from Firebase
		var firebase_players = []
		if multiplayer_manager.players and multiplayer_manager.players.size() > 0:
			firebase_players = multiplayer_manager.players
			print("Found players in multiplayer_manager: ", firebase_players)

		# Process any new players
		for player_name in firebase_players:
			var player_exists = false
			for p in players:
				if p.pname == player_name:
					player_exists = true
					break

			if not player_exists:
				print("Adding player from Firebase: " + player_name)
				var player = Player.new(player_name)
				players.append(player)

				# Update UI (simplified - no longer showing player columns)
				setup_player_ui()
				
				# No longer assigning responses to players from Godot client
				print("Player added to local array: " + player_name)

	# Update local game status and last processed status
	game_status = new_status
	last_processed_status = new_status

	# Handle different game states
	match new_status:
		"Waiting for Players":
			print("\n\n==== PROCESSING WAITING FOR PLAYERS STATE ====")

			# Determine if this is a new game with different players or same players
			var should_show_room_code = await _should_show_room_code_for_new_game()

			if should_show_room_code:
				print("New game with different players detected - showing room code")
				# Show the room code display when waiting for players (new game with different players)
				if room_code_display:
					print("Making room_code_display visible for new game with different players")
					room_code_display.visible = true
			else:
				print("New game with same players detected - NOT hiding room code for now") # Changed log message
				# Keep room code hidden for same players
				if room_code_display:
					# room_code_display.visible = false # THIS LINE IS NOW COMMENTED OUT
					print("RoomCodeDisplay visibility remains unchanged (should be visible if previously set)")

			# Clear any existing game state
			players = []
			played_cards = {}
			current_round = 0
			prompt_sent_for_round = 0

			# Hide the current prompt display until we have a prompt
			if current_prompt_display:
				current_prompt_display.visible = false

			# Hide the winning response display
			if winning_response_display:
				winning_response_display.visible = false

			print("==== END PROCESSING WAITING FOR PLAYERS STATE ====\n\n")

		"Waiting for Prompt":
			print("\n\n==== PROCESSING WAITING FOR PROMPT STATE ====")

			# This is a new state where the web client is waiting for the Godot client to select a prompt
			# Check if we have players
			if players.size() == 0:
				print("ERROR: No players available for Waiting for Prompt state")
				is_processing_state = false
				return

			# IMPORTANT: Clear any cached round data first
			if multiplayer_manager and multiplayer_manager.firebase:
				multiplayer_manager.firebase.clear_round_data()
				print("Cleared cached round data for new prompt selection")

			# IMPORTANT: Update the judge label first with the correct judge
			await update_judge_asks_label()
			print("Updated judge label in Waiting for Prompt state")

			# Always force prompt selection in this state regardless of prompt_sent_for_round
			print("Current round: ", current_round, ", prompt_sent_for_round: ", prompt_sent_for_round)
			print("FORCING new prompt selection for Waiting for Prompt state")

			# Debug the current prompt display state
			if current_prompt_display:
				print("CurrentPromptDisplay before selection - visible: ", current_prompt_display.visible)
				print("CurrentPromptDisplay before selection - text: ", current_prompt_display.text)
			else:
				print("CurrentPromptDisplay is null before prompt selection!")

			# Reset current_prompt and clear any previous prompts
			current_prompt = {}
			if current_prompt_display:
				current_prompt_display.text = ""
				current_prompt_display.visible = false
				print("Cleared current prompt display")

			# Unconditionally select a new prompt
			print("Forcing prompt selection regardless of prompt_sent_for_round value")
			select_and_display_prompt()

			# Wait a short time for prompt to be visible
			await get_tree().create_timer(0.5).timeout

			# Force visibility again after a short delay
			ensure_prompt_visible()

			# Debug the current prompt display state after selection
			if current_prompt_display:
				print("CurrentPromptDisplay after selection - visible: ", current_prompt_display.visible)
				print("CurrentPromptDisplay after selection - text: ", current_prompt_display.text)
			else:
				print("CurrentPromptDisplay is null after prompt selection!")

			# After prompt is selected and visible, change state to Ready
			print("\n\n==== CHANGING GAME STATUS TO: Ready ====")

			# Update Firebase with Ready status
			if multiplayer_manager:
				print("Updating game status via multiplayer_manager to Ready")
				multiplayer_manager.update_game_status("Ready")

				# Use direct Firebase update as backup
				if multiplayer_manager.firebase:
					print("Using direct Firebase update as backup to set status Ready")
					multiplayer_manager.firebase.update_game_data("status", "Ready")

			print("==== END PROCESSING WAITING FOR PROMPT STATE ====\n\n")

		"Ready":
			print("DEBUG: Processing Ready state")

			# Set the flag to not display responses at the start of a new round
			should_display_responses = false
			print("Set should_display_responses to false in Ready state")

			# Reset the response tracking for the new round
			last_displayed_responses_count = 0
			print("Reset response tracking for new round")

			# Clear any existing responses from the previous round
			print("Clearing played cards container in Ready state")
			for child in played_cards_container.get_children():
				child.queue_free() # Ensure proper cleanup
				
			# IMPORTANT: Update the judge label first before selecting prompt
			await update_judge_asks_label()
			print("Updated judge label in Ready state")

			# Ensure the prompt is selected and visible.
			# select_and_display_prompt() was called in "Waiting for Prompt"
			# We just need to ensure it's visible.
			ensure_prompt_visible()

			# Check if we have players
			if players.size() == 0:
				print("ERROR: No players available for Ready state")
				is_processing_state = false
				return

			print("Prompt selection completed. Game is READY. Waiting for judge to start the round.")
			# Ensure RoomCodeDisplay is visible if it should be at this stage
			if room_code_display and not room_code_display.visible:
				if game_status == "Waiting for Players" or game_status == "Waiting for Prompt" or game_status == "Ready": # Check current or previous relevant states
					var should_show = await _should_show_room_code_for_new_game() # Re-evaluate if needed
					if should_show:
						print("Ensuring RoomCodeDisplay is visible in Ready state.")
						room_code_display.visible = true

			# REMOVE AUTOMATIC TRANSITION. Judge will trigger next state ("Waiting for Player Responses")
			# if current_round > 0:
			#	print("Starting timer to transition to Waiting for Player Responses")
			#	await get_tree().create_timer(3.0).timeout
			#
			#	if game_status == "Ready": 
			#		print("\\n\\n==== AUTO-TRANSITIONING TO: Waiting for Player Responses ====")
			#		# ... (removed auto transition logic) ...
			#		print("==== END AUTO-TRANSITION TO WAITING FOR PLAYER RESPONSES ====\\n\\n")
			print("==== END PROCESSING READY STATE (Waiting for Judge) ====\\n\\n")

		"Waiting for Player Responses":
			print("\\\\n\\\\n==== WAITING FOR PLAYER RESPONSES STATE (JUDGE STARTED ROUND) ====")

			# Hide RoomCodeDisplay as the judge has now started the round
			if room_code_display and room_code_display.visible:
				# room_code_display.visible = false # Ensure this is commented
				# print("Hid RoomCodeDisplay as judge started the round") # Ensure this is commented
				pass # Do nothing, keep the room code display visible

			var current_prompt_text = current_prompt.get("prompt", "")
			if current_prompt_text == "" or current_prompt_text == "No prompt available":
				print("WARNING: No prompt found in Waiting for Player Responses state, selecting one now")

				# Force prompt selection
				prompts = load_prompts()
				randomize()
				prompts.shuffle()
				select_and_display_prompt()

				# Wait for prompt to be processed
				await get_tree().create_timer(1.0).timeout
			else:
				print("Prompt already exists: ", current_prompt_text)

			# IMPORTANT: Update the judge label
			await update_judge_asks_label()
			print("Updated judge label in Waiting for Player Responses state")

			# Ensure prompt is visible
			ensure_prompt_visible()

			# Start the response timer
			timer_label.text = "Players are responding..."
			timer_label.visible = true
			judge_timer.wait_time = 30.0
			judge_timer.start()

			# Set the flag to display responses as they arrive
			should_display_responses = true
			print("Set should_display_responses to true in Waiting for Player Responses state")

			# Reset the response tracking for the new response phase
			last_displayed_responses_count = 0
			print("Reset response tracking for new response phase")

			# Clear any existing responses from the previous round
			print("Clearing played cards container for new responses")
			for child in played_cards_container.get_children():
				child.queue_free()

		"Ready for Judging":
			print("\n\n==== READY FOR JUDGING STATE ====")
			# Stop the response timer if it's running
			if not judge_timer.is_stopped():
				judge_timer.stop()

			# Update UI
			timer_label.text = "Judging in progress..."

			# Update the prompt label with the current prompt
			if prompt_label and current_prompt and current_prompt.has("prompt"):
				prompt_label.text = "Prompt: " + current_prompt.get("prompt", "")

			# Set the flag to display responses
			should_display_responses = true
			print("Set should_display_responses to true in Ready for Judging state")

			# Note: We're no longer clearing the played cards container here
			# since we want to display responses as they arrive
			print("Keeping existing responses visible in Ready for Judging state")


		"Winner Chosen":
			# Stop the judging timer if it's running
			if not judge_timer.is_stopped():
				judge_timer.stop()

			# Update UI to show we're no longer judging
			timer_label.text = "Winner chosen!"

			# Set the flag to not display responses
			should_display_responses = false
			print("Set should_display_responses to false in Winner Chosen state")

			# Clear the played cards container when a winner is chosen
			print("Clearing played cards container in Winner Chosen state")
			for child in played_cards_container.get_children():
				child.queue_free()

			# IMPORTANT: Force Firebase data refresh to get latest winner info
			if multiplayer_manager and multiplayer_manager.firebase:
				print("Forcing Firebase data refresh for winner information...")

				# Manually trigger a Firebase poll to get the latest data
				multiplayer_manager.firebase._poll_firebase_data()

				# Wait a moment for the data to be updated
				await get_tree().create_timer(1.0).timeout

				print("Getting winner information from refreshed cache...")
				var winner_name = await multiplayer_manager.get_game_winner()
				var winning_response = await multiplayer_manager.get_winning_response()

				print("Retrieved winner: ", winner_name)
				print("Retrieved winning response: ", winning_response)

				# If we still don't have valid data, try one more refresh
				if (winner_name == "" or winner_name == "No winner available") and (winning_response == "" or winning_response == "No winning response available"):
					print("WARNING: No valid winner data found, trying one more refresh...")
					multiplayer_manager.firebase._poll_firebase_data()
					await get_tree().create_timer(1.0).timeout

					winner_name = await multiplayer_manager.get_game_winner()
					winning_response = await multiplayer_manager.get_winning_response()
					print("After second refresh - Winner: ", winner_name, ", Response: ", winning_response)

				# Display winner
				display_winner(winner_name, winning_response)

		"Game Over":
			print("\n\n==== PROCESSING GAME OVER STATE ====")

			# Stop any running timers
			if not judge_timer.is_stopped():
				judge_timer.stop()

			# Clear the played cards container
			for child in played_cards_container.get_children():
				child.queue_free()

			# Hide the winning response display
			if winning_response_display:
				winning_response_display.visible = false

			# Get the game winner from Firebase
			if multiplayer_manager and multiplayer_manager.firebase:
				print("Getting game winner from Firebase...")

				# Force a data refresh to get the latest winner
				multiplayer_manager.firebase.force_data_refresh()
				await get_tree().create_timer(1.0).timeout

				var game_winner = multiplayer_manager.firebase.get_game_winner()
				print("Game winner: ", game_winner)

				# Display the game over screen
				display_game_over(game_winner)

			print("==== END PROCESSING GAME OVER STATE ====\n\n")

		"Closed":
			# Return to main menu
			get_tree().change_scene_to_file("res://menu.tscn")

	# Reset the processing flag after a short delay
	await get_tree().create_timer(1.0).timeout
	is_processing_state = false
	print("Reset processing flag, ready for next state change")

# Function to load prompts from JSON file with proper structure handling
func load_prompts() -> Array:
	print("\n===== LOADING PROMPTS =====")

	# First try cardGame directory
	var file_path = "res://cardGame/prompts.json"
	print("Attempting to load prompts from: " + file_path)
	var file = FileAccess.open(file_path, FileAccess.READ)

	# If not found, try root directory
	if not file:
		file_path = "res://prompts.json"
		print("First path failed, trying: " + file_path)
		file = FileAccess.open(file_path, FileAccess.READ)

	if file:
		print("File opened successfully")
		var content = file.get_as_text()
		print("File content length: " + str(content.length()))

		var json = JSON.parse_string(content)
		if json != null:
			# Handle different JSON structures
			if typeof(json) == TYPE_ARRAY:
				return json
			elif typeof(json) == TYPE_DICTIONARY and json.has("prompts"):
				return json.prompts
			else:
				print("ERROR: Unrecognized JSON structure")
		else:
			print("ERROR: Failed to parse JSON")
	else:
		print("ERROR: Failed to open prompts file at both locations")

	# Return fallback prompts if loading failed
	print("FALLBACK: Using hardcoded prompts")
	var fallback_prompts = [
		{"prompt": "What's your favorite thing about...?"},
		{"prompt": "Tell us about a time when..."},
		{"prompt": "What would happen if...?"},
		{"prompt": "Who is most likely to...?"},
		{"prompt": "The most awkward thing about ____ is..."}
	]
	return fallback_prompts

# Function to update the judge asks label with the current judge's name
func update_judge_asks_label(prompt_text = null):
	print("\n===== UPDATING JUDGE ASKS LABEL =====")

	# Get the latest judge index from Firebase cache (no more HTTP requests)
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		print("Getting current judge index from Firebase cache...")
		var firebase_judge_index = multiplayer_manager.firebase.get_current_judge_index()
		print("Firebase judge index from cache: ", firebase_judge_index)

		# Update current_judge_index with the cached Firebase value
		if firebase_judge_index >= 0:
			print("Setting current_judge_index to cached Firebase value: ", firebase_judge_index)
			current_judge_index = firebase_judge_index
	
	# Print all players with their indices for debugging
	print("DEBUG: All players in array:")
	for i in range(players.size()):
		print("  Player ", i, ": ", players[i].pname)
	print("DEBUG: Current judge index is: ", current_judge_index)
	
	# Get the JudgeAsksLabel node if we don't have it
	if not judge_asks_label:
		judge_asks_label = get_node_or_null("JudgeAsksLabel")
		
	# Now set the label with the correct judge's name
	if judge_asks_label and players.size() > 0:
		# Important: Make sure index is valid
		if current_judge_index >= 0 and current_judge_index < players.size():
			var judge_name = players[current_judge_index].pname
			print("Setting JudgeAsksLabel with name: ", judge_name)
			
			# If a custom prompt text was provided, use that
			if prompt_text != null:
				judge_asks_label.text = judge_name + " asks: " + prompt_text
			else:
				judge_asks_label.text = judge_name + " asks:"
				
			# Make sure it's visible
			judge_asks_label.visible = true
		else:
			print("ERROR: Current judge index is invalid: ", current_judge_index, " (players size: ", players.size(), ")")
			judge_asks_label.text = "Judge asks:"
			judge_asks_label.visible = true
	else:
		print("WARNING: JudgeAsksLabel not found or no players available")
	
	print("===== JUDGE ASKS LABEL UPDATED =====\n")

# Function to ensure the current prompt is visible and update its text if provided
func ensure_prompt_visible(prompt_text = null):
	print("\n===== ENSURING PROMPT IS VISIBLE =====")
	
	# Make sure the current_prompt_display is valid
	if current_prompt_display:
		# Make it visible
		current_prompt_display.visible = true
		
		# Update the text if provided
		if prompt_text != null:
			current_prompt_display.text = prompt_text
			print("Updated prompt text to: ", prompt_text)
		else:
			print("Kept existing prompt text: ", current_prompt_display.text)
		
		# Debug output
		print("Prompt display is now visible: ", current_prompt_display.visible)
	else:
		push_error("ERROR: current_prompt_display is null in ensure_prompt_visible()")
		print("ERROR: current_prompt_display is null in ensure_prompt_visible()")
	
	print("===== PROMPT VISIBILITY ENSURED =====\n")

# Function to force a new prompt to be selected and displayed
func force_new_prompt():
	print("\n\n==== FORCE NEW PROMPT FUNCTION CALLED ====")

	# Reload the prompts from scratch
	print("Reloading prompts from scratch")
	prompts = load_prompts()

	# Shuffle the prompts thoroughly
	print("Shuffling prompts")
	randomize()
	prompts.shuffle()

	# Get the previous prompt text for comparison
	var previous_prompt_text = ""
	if typeof(current_prompt) == TYPE_DICTIONARY and current_prompt.has("prompt"):
		previous_prompt_text = current_prompt.get("prompt", "")
	print("Previous prompt text: ", previous_prompt_text)

	# Select a random prompt
	var random_index = randi() % prompts.size()
	var new_prompt = prompts[random_index]
	print("Selected new prompt: ", new_prompt)

	# Check if the selected prompt is the same as the previous one
	var selected_prompt_text = ""
	if typeof(new_prompt) == TYPE_DICTIONARY and new_prompt.has("prompt"):
		selected_prompt_text = new_prompt.get("prompt", "")
	else:
		selected_prompt_text = str(new_prompt)

	# If it's the same as the previous prompt, try to select a different one
	if selected_prompt_text == previous_prompt_text and prompts.size() > 1:
		print("WARNING: Selected the same prompt as before, trying again")

		# Try up to 3 times to get a different prompt
		for _attempt in range(3):
			# Get a new random index
			randomize()
			random_index = randi() % prompts.size()
			new_prompt = prompts[random_index]

			# Check if this one is different
			if typeof(new_prompt) == TYPE_DICTIONARY and new_prompt.has("prompt"):
				selected_prompt_text = new_prompt.get("prompt", "")
			else:
				selected_prompt_text = str(new_prompt)

			if selected_prompt_text != previous_prompt_text:
				print("Found a different prompt on attempt ", _attempt + 1)
				break

		print("Final selected prompt: ", new_prompt)

	# Set it as the current prompt
	current_prompt = new_prompt
	print("Set current_prompt to new prompt")

	# Update the display
	if current_prompt_display:
		print("Updating current_prompt_display")
		current_prompt_display.text = new_prompt.get("prompt", "")
		current_prompt_display.visible = true
		print("Set current_prompt_display.text to: ", new_prompt.get("prompt", ""))
		print("Set current_prompt_display.visible = true")
	else:
		print("ERROR: current_prompt_display is null!")

		# Try to find it by node path
		current_prompt_display = get_node_or_null("CurrentPromptDisplay")
		if current_prompt_display:
			print("Found CurrentPromptDisplay by direct path")
			current_prompt_display.text = new_prompt.get("prompt", "")
			current_prompt_display.visible = true
			print("Set CurrentPromptDisplay.text and visible=true")
		else:
			print("CRITICAL ERROR: Could not find CurrentPromptDisplay node!")

	# Send to Firebase
	if multiplayer_manager and multiplayer_manager.firebase:
		print("Sending new prompt to Firebase")

		# Create a simple prompt object with just the text
		var firebase_prompt = {"prompt": new_prompt.get("prompt", "")}
		print("Firebase prompt object: ", firebase_prompt)

		# Use the multiplayer_manager to update the prompt
		multiplayer_manager.update_current_prompt(firebase_prompt)
		print("New prompt sent to Firebase via multiplayer_manager")

		# Double-check with a direct update as a backup
		multiplayer_manager.firebase.update_game_data("current_prompt", firebase_prompt)
		print("New prompt sent to Firebase via direct update (backup)")
	else:
		print("ERROR: multiplayer_manager or firebase not available")

	# Note: Don't set prompt_sent_for_round here to avoid conflicts
	# It will be set by the calling function if needed
	print("Force new prompt complete - prompt_sent_for_round will be set by caller")

	print("==== FORCE NEW PROMPT COMPLETE ====\n\n")

# The prompt selection function that ensures each prompt is truly random for each round
func select_and_display_prompt():
	print("\n\n==== SELECT AND DISPLAY PROMPT FUNCTION CALLED ====")
	print("Current round: ", current_round, ", prompt_sent_for_round: ", prompt_sent_for_round)

	# Step 0: Ensure prompts are loaded
	if prompts.is_empty():
		print("Prompts array is empty. Reloading prompts.")
		prompts = load_prompts()
		if prompts.is_empty():
			printerr("CRITICAL ERROR: Failed to load prompts. Cannot select a prompt.")
			# Optionally, try to set a default error prompt or handle this state
			if multiplayer_manager and multiplayer_manager.firebase:
				var error_prompt_data = {
					"prompt": "Error: No prompts available. Please restart the game.",
					"judge_display_text": "Error: No prompts available."
				}
				multiplayer_manager.firebase.update_game_data("current_prompt", error_prompt_data)
				multiplayer_manager.firebase.update_game_data("prompt_text_only", error_prompt_data.prompt)
			if current_prompt_display:
				current_prompt_display.text = "Error: No prompts available."
				current_prompt_display.visible = true
			if judge_asks_label:
				judge_asks_label.text = "Error: No prompts available."
				judge_asks_label.visible = true
			return # Exit if no prompts can be loaded

	# Step 1: Select a prompt using the sequential method
	if used_prompt_indices.size() >= prompts.size():
		print("All prompts have been used. Resetting used prompts list.")
		used_prompt_indices.clear()
		prompt_selection_index = 0 # Reset index as well

	var selected_prompt_text = ""
	var selected_prompt_obj = {}

	# Ensure prompt_selection_index is within bounds
	if prompt_selection_index >= prompts.size():
		print("prompt_selection_index out of bounds (", prompt_selection_index, "), resetting to 0.")
		prompt_selection_index = 0
		if prompts.is_empty(): # Double check after potential reset
			printerr("CRITICAL ERROR: Prompts became empty during selection index reset.")
			return

	selected_prompt_obj = prompts[prompt_selection_index]
	selected_prompt_text = selected_prompt_obj.get("prompt", "Error: Prompt text not found")
	print("Selected prompt at index ", prompt_selection_index, ": '", selected_prompt_text, "'")

	# Increment for next time, and loop if necessary
	prompt_selection_index += 1
	if prompt_selection_index >= prompts.size():
		prompt_selection_index = 0 # Loop back to the beginning

	# Store the selected prompt locally
	current_prompt = selected_prompt_obj
	print("Stored current_prompt locally: ", current_prompt)

	# Step 2: Format the prompt for the judge's display
	var judge_display_text = selected_prompt_text
	if selected_prompt_obj.has("judge_display_text_format") and selected_prompt_obj.judge_display_text_format:
		judge_display_text = selected_prompt_obj.judge_display_text_format
	print("Judge display text: '", judge_display_text, "'")

	# Step 3: Update the judge asks label (this now fetches judge name from Firebase)
	print("Before await update_judge_asks_label in select_and_display_prompt")
	await update_judge_asks_label(judge_display_text) # Pass the text to be displayed
	print("After await update_judge_asks_label in select_and_display_prompt")

	# Step 4: Ensure the prompt display is visible and updated
	ensure_prompt_visible(selected_prompt_text) # Pass the text to be displayed

	# Step 5: Mark that we've sent a prompt for this round
	prompt_sent_for_round = current_round
	print("Updated prompt_sent_for_round = ", current_round)

	# Step 6: REMOVE/REDUCE Wait before sending to Firebase
	# print("Waiting 2 seconds before uploading to Firebase...") # OLD
	# await get_tree().create_timer(2.0).timeout # OLD
	print("Uploading prompt to Firebase immediately...") # NEW

	# Step 7: Upload to Firebase for web client
	if multiplayer_manager and multiplayer_manager.firebase:
		print("Attempting Firebase upload for prompt...")
		var firebase_prompt = {
			"prompt": selected_prompt_text,
			"judge_display_text": judge_display_text
			# Add any other fields the web client might expect for a prompt
		}
		print("Firebase prompt object to be uploaded: ", firebase_prompt)
		multiplayer_manager.firebase.update_game_data("current_prompt", firebase_prompt)
		multiplayer_manager.firebase.update_game_data("prompt_text_only", selected_prompt_text) # For simpler access by web client if needed
		print("Firebase upload call completed for prompt.")
	else:
		print("Multiplayer manager or Firebase not available, skipping Firebase upload for prompt.")

	print("==== END SELECT AND DISPLAY PROMPT FUNCTION ====\n\n")

# Function to load responses from JSON file with proper structure handling
func load_responses() -> Array:
	print("Loading responses...")
	var file = FileAccess.open("res://cardGame/responses.json", FileAccess.READ)
	if not file:
		file = FileAccess.open("res://responses.json", FileAccess.READ)

	if file:
		var content = file.get_as_text()
		var json = JSON.parse_string(content)

		if json != null:
			# Handle different JSON structures
			if typeof(json) == TYPE_ARRAY:
				print("Loaded", json.size(), "responses (array format)")
				return json
			elif typeof(json) == TYPE_DICTIONARY and json.has("responses"):
				# Check if we need to convert "text" fields to "response"
				var response_array = json.responses
				var converted_responses = []

				for resp in response_array:
					if resp.has("text") and not resp.has("response"):
						var new_resp = resp.duplicate()
						new_resp["response"] = resp.text
						converted_responses.append(new_resp)
					else:
						converted_responses.append(resp)

				print("Loaded and converted", converted_responses.size(), "responses (object format)")
				return converted_responses

	# Return fallback responses if loading failed
	print("Failed to load responses, using fallbacks")
	return [
		{"response": "A suspicious stain."},
		{"response": "A lifetime of sadness."},
		{"response": "An awkward silence."},
		{"response": "The inevitable heat death of the universe."}
	]

# Add a key handler for testing the prompt selection directly
func _input(event):
	# Test key for simulating a Ready status change
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_R:
			print("\n\nMANUAL TEST: Simulating Ready status change\n\n")
			handle_game_state_change("Ready")
		elif event.keycode == KEY_J:
			print("\n\nMANUAL TEST: Simulating Ready for Judging status change\n\n")
			handle_game_state_change("Ready for Judging")
		elif event.keycode == KEY_W:
			print("\n\nMANUAL TEST: Simulating Winner Chosen status change\n\n")
			handle_game_state_change("Winner Chosen")
		elif event.keycode == KEY_P:
			print("\n\nMANUAL TEST: Directly testing prompt selection\n\n")
			select_and_display_prompt()
		elif event.keycode == KEY_N:
			print("\n\nMANUAL TEST: Force new prompt\n\n")
			force_new_prompt()
		elif event.keycode == KEY_T:
			print("\n\nMANUAL TEST: Simulating Waiting for Prompt status change\n\n")
			handle_game_state_change("Waiting for Prompt")
		elif event.keycode == KEY_U:
			print("\n\nMANUAL TEST: Simulating Waiting for Player Responses status change\n\n")
			handle_game_state_change("Waiting for Player Responses")
		elif event.keycode == KEY_F:
			print("\n\nMANUAL TEST: Force Firebase data refresh\n\n")
			if multiplayer_manager and multiplayer_manager.firebase:
				multiplayer_manager.firebase.force_data_refresh()
		elif event.keycode == KEY_C:
			print("\n\nMANUAL TEST: Clear round data\n\n")
			if multiplayer_manager and multiplayer_manager.firebase:
				multiplayer_manager.firebase.clear_round_data()
		elif event.keycode == KEY_G:
			print("\n\nMANUAL TEST: Simulating Game Over status change\n\n")
			handle_game_state_change("Game Over")
		elif event.keycode == KEY_R:
			print("\n\nMANUAL TEST: Simulating new game with different players\n\n")
			if multiplayer_manager and multiplayer_manager.firebase:
				multiplayer_manager.firebase.update_game_data("reset_game", true)
		elif event.keycode == KEY_D:
			# Add current player and prompt debug info
			print("\n\nDEBUG INFO:")
			print("Current prompt: ", current_prompt)
			print("Current round: ", current_round)
			print("Prompt sent for round: ", prompt_sent_for_round)
			print("Game status: ", game_status)
			if current_prompt_display:
				print("Current prompt display visible: ", current_prompt_display.visible)
				print("Current prompt display text: ", current_prompt_display.text)
			else:
				print("Current prompt display not found")
			print("\n")
		elif event.keycode == KEY_J:
			# Manual judge index debug
			print("\n\nMANUAL JUDGE INDEX DEBUG:")
			if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
				if multiplayer_manager.firebase.has_method("debug_get_entire_game_data"):
					print("Getting entire game data...")
					await multiplayer_manager.firebase.debug_get_entire_game_data()
				if multiplayer_manager.firebase.has_method("get_current_judge_index"):
					print("Getting current judge index...")
					var judge_index = await multiplayer_manager.firebase.get_current_judge_index()
					print("Current judge index from Firebase: ", judge_index)
					print("Local current_judge_index: ", current_judge_index)
					var player_names = []
					for p in players:
						player_names.append(p.pname)
					print("Players array: ", player_names)
			print("END JUDGE INDEX DEBUG\n\n")
		elif event.keycode == KEY_R:
			# Manual response check debug
			print("\n\nMANUAL RESPONSE CHECK DEBUG:")
			print("Current game status: ", game_status)
			print("should_display_responses: ", should_display_responses)
			print("last_displayed_responses_count: ", last_displayed_responses_count)
			print("Current children in played_cards_container: ", played_cards_container.get_children().size())

			if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
				print("Manually checking for new responses...")

				# Try direct Firebase call
				print("Making direct Firebase call for submitted responses...")
				var direct_responses = await _get_submitted_responses_direct()
				print("Direct Firebase call returned: ", direct_responses)

				if direct_responses and direct_responses.size() > 0:
					print("Found ", direct_responses.size(), " responses via direct call")
					if should_display_responses:
						print("Forcing display of responses...")
						_on_submitted_responses_updated(direct_responses)
					else:
						print("should_display_responses is false, not displaying")
				else:
					print("No responses found via direct call")

				# Also try the original method
				print("Also trying original method...")
				await _check_for_new_responses()

				# Test if the Firebase signal is working
				print("Testing Firebase signal connection...")
				if multiplayer_manager.firebase.has_signal("submitted_responses_updated"):
					print("Firebase has submitted_responses_updated signal")
					# Try to manually emit the signal with test data
					if direct_responses and direct_responses.size() > 0:
						print("Manually emitting submitted_responses_updated signal with direct responses...")
						multiplayer_manager.firebase.emit_signal("submitted_responses_updated", direct_responses)
				else:
					print("Firebase does NOT have submitted_responses_updated signal")
			print("END RESPONSE CHECK DEBUG\n\n")

# Direct function to get submitted responses from Firebase (for debugging)
func _get_submitted_responses_direct():
	if not is_multiplayer_game or not multiplayer_manager or not multiplayer_manager.firebase:
		print("Cannot make direct Firebase call - not in multiplayer game or Firebase not available")
		return []

	var firebase = multiplayer_manager.firebase
	if firebase.current_game_id == "":
		print("Cannot make direct Firebase call - no game ID")
		return []

	# Create HTTP request
	var http_request = HTTPRequest.new()
	add_child(http_request)

	var url = firebase.current_database_url + "/" + firebase.current_game_id + "/submitted_responses.json"
	print("DIRECT CALL: Getting submitted responses from URL: ", url)

	var error = http_request.request(url)
	if error != OK:
		print("DIRECT CALL: Error requesting submitted responses: ", error)
		http_request.queue_free()
		return []

	# Wait for the request to complete
	var result = await http_request.request_completed

	var response_code = result[1]
	var body = result[3]

	if response_code != 200:
		print("DIRECT CALL: Error getting submitted responses, response code: ", response_code)
		http_request.queue_free()
		return []

	var response_text = body.get_string_from_utf8()
	print("DIRECT CALL: Raw response text: ", response_text)

	var submitted_responses = JSON.parse_string(response_text)
	print("DIRECT CALL: Parsed submitted responses: ", submitted_responses)

	http_request.queue_free()

	if submitted_responses == null:
		print("DIRECT CALL: Submitted responses is null, returning empty array")
		return []

	return submitted_responses

# Add the missing function that updates the timer display
func _update_timer_display():
	# Update the timer display based on the current game state
	if judge_timer.is_stopped():
		# Hide the timer label when the timer is not running
		if timer_label:
			timer_label.visible = false
		return

	# Make sure the timer label is visible when the timer is running
	if timer_label and not timer_label.visible:
		timer_label.visible = true

	# Calculate remaining time
	var time_left = int(judge_timer.time_left)

	# Only update the UI if the time has changed
	if time_left == last_timer_update:
		return

	# Store the current time
	last_timer_update = time_left

	# Update display based on game state
	match game_status:
		"Waiting for Player Responses":
			timer_label.text = "Players are responding... " + str(time_left) + " seconds"
		"Round Over":
			timer_label.text = "Waiting for judging... " + str(time_left) + " seconds"
		_:
			timer_label.text = "Time remaining: " + str(time_left) + " seconds"

# Function to manually check for new responses (now mainly for debugging)
func _check_for_new_responses():
	print("_check_for_new_responses called (manual check) - game_status: ", game_status)

	# This function is now mainly for manual/debug purposes since we use Firebase streaming
	# Only check if we're in a multiplayer game with Firebase
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		print("Getting submitted responses from cache...")

		# Get submitted responses from cache (no more HTTP requests)
		var submitted_responses = multiplayer_manager.get_submitted_responses()
		print("Cache returned: ", submitted_responses)

		# Update the should_display_responses flag based on game state
		should_display_responses = (game_status == "Waiting for Player Responses" or game_status == "Ready for Judging")
		print("should_display_responses: ", should_display_responses)

		if submitted_responses and submitted_responses.size() > 0:
			print("Found ", submitted_responses.size(), " responses in cache")
			if should_display_responses:
				_on_submitted_responses_updated(submitted_responses)
			else:
				print("Not displaying responses - wrong game state")
		else:
			print("No responses found in cache")
	else:
		print("Not checking responses - not in multiplayer mode or Firebase not available")

# Multiplayer functions
func start_multiplayer_game():
	# Clear any existing game state
	players = []
	played_cards = {}

	# Start waiting for players
	waiting_for_players = true
	game_status = "Waiting for Players"

	# Update UI to show waiting state
	if prompt_label:
		prompt_label.text = ""  # Clear any previous text
	if judge_label:
		judge_label.text = ""   # Clear any previous text

	# Hide the current prompt display until we have a prompt
	if current_prompt_display:
		current_prompt_display.visible = false

	# Hide the winning response display
	if winning_response_display:
		winning_response_display.visible = false

	# Show the room code display
	if room_code_display:
		print("cardGame: Making room_code_display visible")
		room_code_display.visible = true

	# Connect to the game_created signal if not already connected
	if multiplayer_manager and not multiplayer_manager.game_created.is_connected(self.show_room_code):
		multiplayer_manager.game_created.connect(self.show_room_code)

	# Start the multiplayer game
	if multiplayer_manager:
		multiplayer_manager.start_new_game()

		# Update game status in Firebase
		print("\n\nSetting initial game status to: Waiting for Players\n\n")
		multiplayer_manager.update_game_status(game_status)

		# Use direct Firebase update as backup
		if multiplayer_manager.firebase:
			print("Using direct Firebase update as backup")
			multiplayer_manager.firebase.update_game_data("status", "Waiting for Players")

	# Load prompts and responses
	prompts = load_prompts()
	responses = load_responses()

	# Debug information
	print("Loaded prompts: ", prompts.size())
	print("Loaded responses: ", responses.size())

	# Shuffle the cards
	randomize()
	prompts.shuffle()
	responses.shuffle()

# Initialize players with given names
func initialize_players(player_names: Array):
	players = []  # Clear any existing players
	for pname in player_names:
		var player = Player.new(pname)  # Create new player with name
		players.append(player)
		distribute_cards(player)  # Give them cards
		print("Player", player.pname, "has", player.hand.size(), "cards")

# Distribute cards to a player
func distribute_cards(player):
	for i in range(5):  # Give each player 5 cards
		if responses.size() > 0:
			var card = responses.pop_back()
			player.add_card_to_hand(card)
		else:
			print("WARNING: Ran out of response cards!")

func start_game():
	# Note: Don't hide room code display here - let it stay visible until round actually starts
	# Room code will be hidden when status changes to "Waiting for Player Responses"
	print("Starting game - keeping room code visible until round begins")

	# Set initial game state
	current_round = 1

	# Set initial game status
	# Only set game status if we're not in a multiplayer game
	# In multiplayer games, the status is controlled by Firebase
	if not is_multiplayer_game:
		game_status = "Round 1"

	# Reset judge-related variables
	current_judge_index = 0

	# Update game status in Firebase
	if is_multiplayer_game and multiplayer_manager:
		multiplayer_manager.update_game_status(game_status)
		multiplayer_manager.update_current_round(current_round)

	# Initial round
	start_round()

# Start a new round of the game
func start_round():
	print("\n\n==== STARTING ROUND " + str(current_round) + " ====")

	# Check if UI elements exist
	if not prompt_label or not judge_label or not timer_label or not played_cards_container:
		push_error("UI elements not found. Cannot start round.")
		return

	# Check if players array is valid
	if players.size() == 0:
		push_error("No players available")
		return

	# Print judge information at the start of each round
	print("\n===== ROUND START JUDGE INFO =====")
	print("Current judge index: ", current_judge_index)

	# Print all players for debugging
	print("All players in array:")
	for i in range(players.size()):
		print("  Player ", i, ": ", players[i].pname)

	# Get the current judge's name
	if players.size() > current_judge_index:
		var judge_name = players[current_judge_index].pname
		print("Current judge at start of round: ", judge_name, " at index ", current_judge_index)
	else:
		print("ERROR: Invalid judge index at start of round: ", current_judge_index)

	# If this is a multiplayer game, try to get the judge index from Firebase
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		print("Attempting to get current judge index from Firebase at start of round...")
		if multiplayer_manager.firebase.has_method("get_current_judge_index"):
			print("About to call get_current_judge_index() at start of round...")
			var firebase_judge_index_raw = await multiplayer_manager.firebase.get_current_judge_index()
			print("Raw Firebase judge index at start of round: ", firebase_judge_index_raw, " (type: ", typeof(firebase_judge_index_raw), ")")

			# Force conversion to int if it's a float
			var firebase_judge_index = firebase_judge_index_raw
			if firebase_judge_index_raw is float:
				firebase_judge_index = int(firebase_judge_index_raw)
				print("Manually converted float to int at start of round: ", firebase_judge_index)

			print("Final Firebase judge index at start of round: ", firebase_judge_index)

			# Print all players for debugging
			print("All players in array:")
			for i in range(players.size()):
				print("  Player ", i, ": ", players[i].pname)

			# IMPORTANT: Force the judge index to be the one from Firebase
			# Use the Firebase value directly
			if firebase_judge_index != null and firebase_judge_index >= 0 and players.size() > firebase_judge_index:
				var judge_name = players[firebase_judge_index].pname
				print("Current judge from Firebase at start of round is: ", judge_name, " at index ", firebase_judge_index)

				# Update the JudgeAsksLabel directly with the Firebase judge
				if not judge_asks_label:
					judge_asks_label = get_node_or_null("JudgeAsksLabel")

				if judge_asks_label:
					judge_asks_label.text = judge_name + " asks:"
					judge_asks_label.visible = true
					print("DIRECTLY updated JudgeAsksLabel at start of round to: ", judge_asks_label.text)
				else:
					print("ERROR: Could not find JudgeAsksLabel at start of round")

				# Now update the current_judge_index for future use
				if firebase_judge_index != current_judge_index:
					print("MISMATCH: Firebase judge index differs from local judge index!")
					current_judge_index = firebase_judge_index
					print("Updated current_judge_index to match Firebase: ", current_judge_index)
			else:
				print("ERROR: Invalid Firebase judge index at start of round: ", firebase_judge_index, " for players array of size ", players.size())
				# Try with index 1 as fallback (since you mentioned index 0 is wrong)
				if players.size() > 1:
					var judge_name = players[1].pname
					print("FALLBACK: Using player at index 1 as judge at start of round: ", judge_name)
					if judge_asks_label:
						judge_asks_label.text = judge_name + " asks:"
						judge_asks_label.visible = true
						print("Updated JudgeAsksLabel with fallback to: ", judge_asks_label.text)
				# If index 1 doesn't work, try index 0
				elif players.size() > 0:
					var judge_name = players[0].pname
					print("FALLBACK: Using player at index 0 as judge: ", judge_name)
					if judge_asks_label:
						judge_asks_label.text = judge_name + " asks:"
						judge_asks_label.visible = true
						print("Updated JudgeAsksLabel with fallback to: ", judge_asks_label.text)
			print("===== END JUDGE DEBUG INFO =====\n")

	# Reset played cards for this round
	played_cards = {}

	# IMPORTANT: Reset the prompt sent flag for the new round
	# This ensures a new prompt will be selected for this round
	prompt_sent_for_round = 0
	print("Reset prompt_sent_for_round to 0 for new round " + str(current_round))

	# IMPORTANT: Clear the current prompt to force a new selection
	current_prompt = {"prompt": ""}
	print("Cleared current_prompt to force new selection")

	# Update game status
	game_status = "Round " + str(current_round)

	# Clear the played cards container
	for child in played_cards_container.get_children():
		child.queue_free()

	# If this is a multiplayer game, wait for the "Ready" status from Firebase
	if is_multiplayer_game and multiplayer_manager:
		# Update Firebase with current round
		multiplayer_manager.update_game_status("Waiting for Players")
		multiplayer_manager.update_current_round(current_round)
		print("Updated Firebase with current_round = " + str(current_round))

		# Wait for the "Ready" status
		prompt_label.text = ""

		# Hide the current prompt display until we have a new prompt
		if current_prompt_display:
			current_prompt_display.visible = false
			print("Hidden current_prompt_display until new prompt is selected")

		# No longer display the room code in the judge_label
		judge_label.text = ""

		# Force a reload of prompts to ensure we get fresh ones
		prompts = load_prompts()
		randomize()
		prompts.shuffle()
		print("Reloaded and shuffled prompts array for new round")

		print("==== END STARTING ROUND " + str(current_round) + " ====\n\n")
		return

	# For local games, continue with the round setup

	# Always select a new prompt for each round
	# Check if we need to reload prompts (only if they're depleted)
	if prompts.size() == 0:
		print("Prompts array depleted, reloading prompts")
		prompts = load_prompts()
		randomize()
		prompts.shuffle()

	# Always select and display a new prompt
	print("Forcing new prompt selection for round " + str(current_round))
	select_and_display_prompt()

	# Note: prompt_sent_for_round is set inside select_and_display_prompt()

	# Update UI
	judge_label.text = "Round: " + str(current_round)

	# Deal new cards to all players
	for player in players:
		if player.hand.size() < 5:
			# Deal cards to all players
			while player.hand.size() < 5 and responses.size() > 0:
				var card = responses.pop_back()
				player.add_card_to_hand(card)

			# Update hand display
			update_player_hand(player)

	# Update game status to waiting for responses
	if not is_multiplayer_game:
		game_status = "Waiting for Player Responses"

		# Start response phase
		timer_label.text = "Players are responding..."

		# Set a timer for response phase (30 seconds)
		judge_timer.wait_time = 30.0
		judge_timer.start()

func _on_next_round():
	print("\n\n==== NEXT ROUND FUNCTION CALLED ====")

	# Clear played cards container
	for child in played_cards_container.get_children():
		child.queue_free()

	# Hide the winning response display
	if winning_response_display:
		winning_response_display.visible = false
		
	# Use our centralized function to start a new round
	start_round()

	# Check for game end
	for player in players:
		if player.score >= 5: # 5 wins needed (changed from 7)
			print("Player " + player.pname + " has reached 5 points, showing game over")
			show_game_over(player)
			return

	# Update game status to Round Over
	game_status = "Round Over"
	if is_multiplayer_game and multiplayer_manager:
		multiplayer_manager.update_game_status(game_status)
		print("Updated game status to Round Over")

		# Wait 5 seconds before starting new round
		print("Waiting 5 seconds before starting new round")
		await get_tree().create_timer(5.0).timeout

	# Increment round counter
	current_round += 1
	print("Incremented current_round to " + str(current_round))

	# IMPORTANT: Reset the prompt sent flag for the new round
	# This ensures a new prompt will be selected for this round
	prompt_sent_for_round = 0
	print("Reset prompt_sent_for_round to 0 for new round " + str(current_round))

	# IMPORTANT: Clear the current prompt to force a new selection
	current_prompt = {"prompt": ""}
	print("Cleared current_prompt to force new selection")

	# Force a reload of prompts to ensure we get fresh ones
	prompts = load_prompts()
	randomize()
	prompts.shuffle()
	print("Reloaded and shuffled prompts array for new round")

	# Start new round
	start_round()

	print("==== END NEXT ROUND FUNCTION ====\n\n")

func show_game_over(winner_player):
	# Clear the UI
	for child in played_cards_container.get_children():
		child.queue_free()

	# Hide the winning response display
	if winning_response_display:
		winning_response_display.visible = false

	# Update game status to Game Over
	game_status = "Game Over"
	if is_multiplayer_game and multiplayer_manager:
		multiplayer_manager.update_game_status(game_status)
		multiplayer_manager.update_game_winner(winner_player.pname)

	# Create game over container
	var game_over_container = VBoxContainer.new()
	game_over_container.name = "game_over_display"
	game_over_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	game_over_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	game_over_container.alignment = 1 # Center

	# Create game over label
	var game_over_label = Label.new()
	game_over_label.text = "GAME OVER"
	game_over_label.horizontal_alignment = 1 # Center
	game_over_label.add_theme_font_size_override("font_size", 48)
	game_over_container.add_child(game_over_label)

	# Create winner label
	var winner_label = Label.new()
	winner_label.text = winner_player.pname + " WINS THE GAME!"
	winner_label.horizontal_alignment = 1 # Center
	winner_label.add_theme_font_size_override("font_size", 32)
	game_over_container.add_child(winner_label)

	# Create score label
	var score_label = Label.new()
	score_label.text = "Final Score: " + str(winner_player.score) + " points"
	score_label.horizontal_alignment = 1 # Center
	game_over_container.add_child(score_label)

	# Add restart button
	var restart_button = Button.new()
	restart_button.text = "Play Again"
	restart_button.pressed.connect(get_tree().reload_current_scene)
	game_over_container.add_child(restart_button)

	# Add to the UI
	played_cards_container.add_child(game_over_container)

# Determine if we should show room code for a new game
func _should_show_room_code_for_new_game() -> bool:
	print("Determining if room code should be shown for new game...")

	# If we don't have a multiplayer manager or firebase, default to showing room code
	if not multiplayer_manager or not multiplayer_manager.firebase:
		print("No multiplayer manager or firebase - defaulting to show room code")
		return true

	# Check if this is a completely new game (no existing room code) or if room code changed
	var current_room_code = multiplayer_manager.firebase.current_room_code
	print("Current room code: ", current_room_code)

	# If we have a stored room code from previous game, compare it
	if room_code != "" and room_code != current_room_code:
		print("Room code changed from '", room_code, "' to '", current_room_code, "' - showing room code")
		room_code = current_room_code  # Update our stored room code
		return true
	elif room_code == "":
		print("No previous room code stored - showing room code for new game")
	
		room_code = current_room_code  # Store the room code
		return true
	else:
		print("Same room code as before - this is likely a new game with same players")
		return false

# Display game over screen for multiplayer games
func display_game_over(winner_name: String):
	print("Displaying game over screen for winner: ", winner_name)

	# Clear the UI
	for child in played_cards_container.get_children():
		child.queue_free()

	# Hide other UI elements
	if winning_response_display:
		winning_response_display.visible = false

	# Create game over container
	var game_over_container = VBoxContainer.new()
	game_over_container.alignment = BoxContainer.ALIGNMENT_CENTER

	# Create title label
	var title_label = Label.new()
	title_label.text = "GAME OVER"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 48)
	title_label.add_theme_color_override("font_color", Color.YELLOW)
	game_over_container.add_child(title_label)

	# Create winner label
	var winner_label = Label.new()
	winner_label.text = winner_name + " WINS THE GAME!"
	winner_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	winner_label.add_theme_font_size_override("font_size", 36)
	winner_label.add_theme_color_override("font_color", Color.GREEN)
	game_over_container.add_child(winner_label)

	# Create congratulations label
	var congrats_label = Label.new()
	congrats_label.text = "Congratulations! You reached 5 points first!"
	congrats_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	congrats_label.add_theme_font_size_override("font_size", 24)
	game_over_container.add_child(congrats_label)

	# Create timer label for auto-hide
	var auto_hide_label = Label.new()
	auto_hide_label.text = "This message will disappear in 8 seconds..."
	auto_hide_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	auto_hide_label.add_theme_font_size_override("font_size", 16)
	auto_hide_label.add_theme_color_override("font_color", Color.GRAY)
	game_over_container.add_child(auto_hide_label)

	# Add to the UI
	played_cards_container.add_child(game_over_container)

	# Auto-hide after 8 seconds
	await get_tree().create_timer(8.0).timeout

	# Remove the game over display
	if game_over_container and is_instance_valid(game_over_container):
		game_over_container.queue_free()
		print("Game over display auto-hidden after 8 seconds")

	# Update prompt and judge labels
	prompt_label.text = "Game Over"
	judge_label.text = "Waiting for leader to start a new game"

func display_winner(winner_name, winning_response):
	print("\\n\\n==== DISPLAY WINNER FUNCTION CALLED ====")
	print("Winner: ", winner_name, " with response: ", winning_response)
	print("Current round (before increment): ", current_round)

	highlight_winning_response(winner_name, winning_response)

	print("Waiting 6 seconds before moving to the next round...")
	await get_tree().create_timer(6.0).timeout

	if winning_response_display:
		winning_response_display.visible = false

	if game_status == "Winner Chosen" and multiplayer_manager:
		print("\\n\\n==== TRANSITIONING TO WAITING FOR PROMPT FOR NEXT ROUND ====")

		current_round += 1
		print("Incremented current_round to: ", current_round)

		prompt_sent_for_round = 0 ## Reset to ensure the new round definitely needs a prompt
		print("Reset prompt_sent_for_round to 0")

		current_prompt = {"prompt": ""} ## Clear current prompt to force new selection
		print("Cleared current_prompt")

		if multiplayer_manager.firebase:
			multiplayer_manager.firebase.cached_submitted_responses = [] ## Clear for new round
			print("Cleared cached submitted responses for new round")

		last_displayed_responses_count = 0
		should_display_responses = false
		print("Reset response tracking for new round")

		if current_prompt_display:
			current_prompt_display.visible = false ## Hide until new prompt is selected
			print("Hidden current_prompt_display")

		multiplayer_manager.update_current_round(current_round) ## Update Firebase
		print("Updated current_round in Firebase to: ", current_round)

		## DO NOT select or force a prompt here.
		## Simply update the status to "Waiting for Prompt".
		## The handle_game_state_change("Waiting for Prompt") function will take care of selecting the new prompt.
		multiplayer_manager.update_game_status("Waiting for Prompt")
		print("Updated game status to: Waiting for Prompt. The handler will now select the prompt.")

		print("==== END TRANSITIONING TO WAITING FOR PROMPT ====\n\n")

# Function to highlight the winning response in the UI
func highlight_winning_response(winner_name, winning_response):
	print("\n===== HIGHLIGHTING WINNING RESPONSE =====")
	print("Winner: ", winner_name, " with response: ", winning_response)
	
	# Clear any existing cards in the container
	for child in played_cards_container.get_children():
		child.queue_free()
	
	# If winning_response_display exists, use it to show the winner
	if winning_response_display:
		# Set text for winner display
		winning_response_display.get_node("VBoxContainer/WinnerName").text = winner_name + " wins!"
		winning_response_display.get_node("VBoxContainer/WinningCard").text = winning_response
		
		# Make it visible
		winning_response_display.visible = true
		print("Set winning_response_display visible with winner info")
	else:
		print("WARNING: winning_response_display not found, creating fallback display")
		
		# Create a fallback display if the winning_response_display is missing
		var container = VBoxContainer.new()
		container.name = "WinnerContainer"
		
		# Add winner name label
		var winner_label = Label.new()
		winner_label.text = winner_name + " wins!"
		winner_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		winner_label.add_theme_font_size_override("font_size", 24)
		winner_label.add_theme_color_override("font_color", Color.YELLOW)
		container.add_child(winner_label)
		
		# Add winning response label
		var response_label = Label.new()
		response_label.text = winning_response
		response_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		response_label.add_theme_font_size_override("font_size", 18)
		container.add_child(response_label)
		
		# Add to the UI
		played_cards_container.add_child(container)
	
	# Make sure the prompt is still visible
	ensure_prompt_visible()

	print("===== WINNING RESPONSE HIGHLIGHTED =====\n")

# Missing signal handler functions
func _on_player_response_used(player_name, response_index):
	print("Player response used signal received: ", player_name, " used response ", response_index)
	# This signal indicates a player has used a response card
	# Add any specific handling logic here if needed

func _on_judge_index_updated(new_judge_index):
	print("Judge index updated signal received: ", new_judge_index)
	# Update the local judge index
	current_judge_index = new_judge_index
	# Update the judge label with the new judge
	update_judge_asks_label()

func _on_players_updated(players_data):
	print("Players updated signal received: ", players_data)
	# Update the local players array based on Firebase data
	# This is handled in the main game state handler, so we can keep this simple
	print("Players data updated from Firebase")

func _on_winner_updated(winner_name):
	print("Winner updated signal received: ", winner_name)
	# Handle winner updates from Firebase
	print("Winner updated to: ", winner_name)

func _on_winning_response_updated(response_data):
	print("Winning response updated signal received: ", response_data)
	# Handle winning response updates from Firebase
	print("Winning response updated: ", response_data)

func _on_new_game_different_players():
	print("NEW GAME DIFFERENT PLAYERS: Received signal for new game with different players")

	# Force the room code display to be shown for the new game
	if room_code_display:
		print("Showing room code display for new game with different players")
		room_code_display.visible = true

		# Update the room code display with the new room code
		if multiplayer_manager and multiplayer_manager.firebase:
			var new_room_code = multiplayer_manager.firebase.current_room_code
			room_code_display.set_room_code(new_room_code)
			print("Updated room code display with new room code: ", new_room_code)
